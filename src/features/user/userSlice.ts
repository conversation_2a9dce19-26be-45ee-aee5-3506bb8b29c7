/**
 * User Redux Slice
 *
 * Manages user authentication state and profile data
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { UserState, UserProfile, LoginCredentials } from '@/types/user';
import { createAuthService } from '@/services/authService';
import { t } from 'i18next';

// Initial state
const initialState: UserState = {
	isInitialized: false,
	isAuthenticated: false,
	isLoading: false,
	user: null,
	token: null,
	error: null,
};

// Async thunks
export const loginUser = createAsyncThunk(
	'user/dev-login',
	async (credentials: LoginCredentials, { rejectWithValue }) => {
		try {
			const authService = await createAuthService();
			const userProfile = await authService.login(credentials.email);
			return userProfile;
		} catch (error) {
			const errorMessage = error instanceof Error ? error.message : 'Lo<PERSON> failed';
			return rejectWithValue(errorMessage);
		}
	},
);

export const fetchUser = createAsyncThunk('user/fetch', async (_, { rejectWithValue }) => {
	try {
		const authService = await createAuthService();
		// If no token yet (e.g., app just launched and user not logged in), resolve with null quietly
		const token = await authService.getToken();
		if (!token) {
			return null;
		}
		const userProfile = await authService.fetchUser();
		return userProfile;
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : 'Fetch user failed';
		return rejectWithValue(errorMessage);
	}
});

export const logoutUser = createAsyncThunk('user/logout', async () => {
	const authService = await createAuthService();
	await authService.logout();
});

// User slice
const userSlice = createSlice({
	name: 'user',
	initialState,
	reducers: {
		logout: (state) => {
			state.isAuthenticated = false;
			state.user = null;
			state.token = null;
			state.error = null;
		},
		clearError: (state) => {
			state.error = null;
		},
		setUser: (state, action: PayloadAction<UserProfile>) => {
			console.log('👤 User data updated in Redux store:', action.payload);
			state.user = action.payload;
			state.isAuthenticated = true;
			state.error = null;
		},
		setToken: (state, action: PayloadAction<string>) => {
			console.log('🔑 Token updated in Redux store');
			state.token = action.payload;
			state.isAuthenticated = true;
			state.error = null;
		},
	},
	extraReducers: (builder) => {
		// Login user
		builder
			.addCase(loginUser.pending, (state) => {
				state.isLoading = true;
				state.error = null;
				state.user = null;
			})
			.addCase(loginUser.fulfilled, (state, action) => {
				console.log('🔐 Login successful - user profile returned');
				state.isLoading = false;
				state.isAuthenticated = true;
				state.user = action.payload as UserProfile;
				state.error = null;
			})
			.addCase(loginUser.rejected, (state, action) => {
				state.isLoading = false;
				state.isAuthenticated = false;
				state.user = null;
				state.token = null;
				state.error = action.payload as string;
			});

		// Verify email
		builder
			.addCase(fetchUser.pending, (state) => {
				state.isLoading = true;
				state.error = null;
				state.isInitialized = true;
				state.user = null;
				state.isAuthenticated = false;
				console.log('User profile fetch initiated...');
			})
			.addCase(fetchUser.fulfilled, (state, action) => {
				state.isLoading = false;
				if (action.payload === null) {
					// No token present; not an error state. Just mark not authenticated.
					state.isAuthenticated = false;
					state.error = null;
				} else {
					state.isAuthenticated = true;
					state.user = action.payload;
					state.error = null;
				}
			})
			.addCase(fetchUser.rejected, (state, action) => {
				console.error('❌ Error fetching user profile:', action.payload);
				state.isLoading = false;
				state.error = action.payload as string;
				state.user = null;
				state.isAuthenticated = false;
			});

		// Logout user
		builder
			.addCase(logoutUser.pending, (state) => {
				state.isLoading = true;
				state.error = null;
			})
			.addCase(logoutUser.fulfilled, (state) => {
				state.isLoading = false;
				state.isAuthenticated = false;
				state.user = null;
				state.error = null;
				state.isInitialized = false;
				console.log('🚪 User logged out successfully');
			})
			.addCase(logoutUser.rejected, (state, action) => {
				state.isLoading = false;
				state.error = action.payload as string;
			});
	},
});

export const { logout, clearError, setUser, setToken } = userSlice.actions;
export default userSlice.reducer;

// Selectors
export const selectUser = (state: { user: UserState }) => state.user.user;
export const selectIsAuthenticated = (state: { user: UserState }) => state.user.isAuthenticated;
export const selectIsLoading = (state: { user: UserState }) => state.user.isLoading;
export const selectError = (state: { user: UserState }) => state.user.error;
export const selectToken = (state: { user: UserState }) => state.user.token;
export const selectIsInitialized = (state: { user: UserState }) => state.user.isInitialized;
