/**
 * User Redux Slice
 *
 * Manages user authentication state and profile data
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { t } from 'i18next';
import { Post } from './model/post';
import { PostsState } from './model/state';
import { PostService } from '@/services/PostService';

const initialState: PostsState = {
	isInitialized: false,
	loading: false,
	error: null,
	posts: [],
};

export const fetchPosts = createAsyncThunk('posts/fetch', async (_, { rejectWithValue }) => {
	try {
		const service = new PostService();
		const response = await service.fetchPosts();
		return response;
	} catch (error) {
		console.error('Error fetching posts:', error);
		console.log('Error fetching posts:', error.message);
		console.log(error);
		const errorMessage = error instanceof Error ? error.message : 'Post fetch failed';
		return rejectWithValue(errorMessage);
	}
});

const postsSlice = createSlice({
	name: 'posts',
	initialState,
	reducers: {
		fetchPosts(state) {
			state.loading = true;
			state.error = null;
		},
		fetchPostsSuccess(state, action: PayloadAction<Post[]>) {
			state.loading = false;
			state.posts = action.payload;
		},
		fetchPostsFailure(state, action: PayloadAction<string>) {
			state.loading = false;
			state.error = action.payload;
		},
	},
	extraReducers: (builder) => {
		builder
			.addCase(fetchPosts.pending, (state) => {
				state.loading = true;
				state.isInitialized = true;
				state.error = null;
			})
			.addCase(fetchPosts.fulfilled, (state, action) => {
				state.loading = false;
				console.log('Fetched posts:', action.payload[0]);
				state.posts = action.payload;
			})
			.addCase(fetchPosts.rejected, (state, action) => {
				state.loading = false;
				state.error = action.payload as string;
			});
	},
});

export default postsSlice.reducer;
export const selectPosts = (state: { posts: PostsState }) => state.posts.posts;
export const selectIsLoading = (state: { posts: PostsState }) => state.posts.loading;
export const selectIsInitialized = (state: { posts: PostsState }) => state.posts.isInitialized;
export const selectError = (state: { posts: PostsState }) => state.posts.error;
