// Load environment-specific config
const getBaseConfig = () => {
	if (__DEV__) {
		return require('../../app.config.dev.json');
	} else {
		return require('../../app.config.prod.json');
	}
};

const baseConfig = getBaseConfig();

export interface EnvConfig {
	ENV_NAME: string;
	API_URL: string; // from .env
	X_PUBLIC_KEY: string; // from .env
	DEBUG: boolean; // from .env

	CONFIG_VERSION: string; // from JSON
	FEATURE_X_ENABLED: boolean; // from JSON
	AUTH_REDIRECT_URL: string; // from JSON - environment-appropriate redirect URL
}

const getEnv = (): EnvConfig => {
	const extra = (typeof process !== 'undefined' && process.env) ? process.env as any : {} as any;

	// Get the appropriate API URL based on environment
	const getApiUrl = (): string => {
		// If explicitly set via environment variable, use that
		if (extra.EXPO_PUBLIC_API_URL) {
			return extra.EXPO_PUBLIC_API_URL;
		}

		// In development, use middleware server URL with platform-specific host
		if (__DEV__) {
			// For Android emulator, use the special host IP
			// For iOS simulator, localhost works
			const { Platform } = require('react-native');
			if (Platform.OS === 'android') {
				return 'http://********:8000/api';
			} else {
				return 'http://localhost:8000/api';
			}
		} else {
			// In production, use your production URL
			return 'https://your-production-middleware.com';
		}
	};

	return {
		ENV_NAME: extra.EXPO_PUBLIC_ENV_NAME ?? 'NO FILE!',
		API_URL: getApiUrl(),
		X_PUBLIC_KEY: extra.EXPO_PUBLIC_X_PUBLIC_KEY ?? '',
		DEBUG: typeof extra.EXPO_PUBLIC_DEBUG === 'string'
            ? extra.EXPO_PUBLIC_DEBUG.toLowerCase() === 'true'
            : !!extra.EXPO_PUBLIC_DEBUG,

		// Environment-specific config.json
		CONFIG_VERSION: baseConfig.CONFIG_VERSION ?? '0.0.0',
		FEATURE_X_ENABLED: baseConfig.FEATURE_X_ENABLED ?? false,
		AUTH_REDIRECT_URL: baseConfig.AUTH_REDIRECT_URL ?? 'learningcoachcommunity://auth-callback',
	};
};

export default getEnv();
