import { ThemeProvider } from '@react-navigation/native';
import { DefaultTheme } from '@/theme';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import 'react-native-reanimated';

import { Provider, useSelector, useDispatch } from 'react-redux';
import { store } from '@/store';

import { I18nextProvider } from 'react-i18next';
import i18n, { useI18n } from '@/translations';
import { useColorScheme, ActivityIndicator, View } from 'react-native';

import React, { useEffect } from 'react';
import type { AppDispatch } from '@/store';
import {
	selectIsLoading,
	selectUser,
	selectIsInitialized,
	fetchUser,
} from '@/features/user/userSlice';
import { LoadingIndicator } from '@/components/LoadingIndicator';
function GuardedStack() {
	const isLoading = useSelector(selectIsLoading);
	const isInitialized = useSelector(selectIsInitialized);
	const user = useSelector(selectUser);
	const dispatch = useDispatch<AppDispatch>();

	useEffect(() => {
		if (!isInitialized && !isLoading) {
			dispatch(fetchUser());
		}
	}, [isInitialized, isLoading, dispatch]);

	if (isLoading || !isInitialized) {
		return (
			<View style={{ flex: 1, alignItems: 'center', justifyContent: 'center' }}>
				<LoadingIndicator />
			</View>
		);
	}

	return (
		<Stack>
			{/* Unauthenticated routes */}
			<Stack.Protected guard={!user} fallback={{ to: '/' }}>
				<Stack.Screen name='login' />
				<Stack.Screen name='auth' />
			</Stack.Protected>

			{/* Authenticated routes */}
			<Stack.Protected guard={user != null} fallback={{ to: '/login' }}>
				<Stack.Screen name='(tabs)' options={{ headerShown: false, title: 'Home' }} />
				<Stack.Screen name='+not-found' />
				<Stack.Screen name='hello' options={{ title: 'Hello Screen' }} />
				<Stack.Screen name='social/post-composer' options={{ presentation: 'modal' }} />
			</Stack.Protected>
		</Stack>
	);
}

export default function RootLayout() {
	const colorScheme = useColorScheme();
	const [fontsLoaded] = useFonts({
		SpaceMono: require('@assets/fonts/SpaceMono-Regular.ttf'),
		FontAwesome7Jelly: require('@assets/fonts/FontAwesome7Jelly.otf'),
		FontAwesome7JellyFill: require('@assets/fonts/FontAwesome7JellyFill.otf'),
	});
	const [translationsLoaded] = useI18n();

	if (!fontsLoaded || !translationsLoaded) {
		return null;
	}

	return (
		<ThemeProvider value={colorScheme === 'dark' ? DefaultTheme : DefaultTheme}>
			<Provider store={store}>
				<I18nextProvider i18n={i18n}>
					<GuardedStack />
				</I18nextProvider>
			</Provider>
			<StatusBar style='auto' />
		</ThemeProvider>
	);
}
