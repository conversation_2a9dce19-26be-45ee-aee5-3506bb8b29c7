import { ApiFactory } from '@/services/apiFactory';
import { Routes, buildRouteUrl } from '@/api/routes';
import { secureStorageRepository } from '@/repositories/secureStorageRepository';
import type { AuthData } from '@/repositories/secureStorageRepository';
import { Platform } from 'react-native';

export interface AuthService {
	getLoginUrl(state?: string): string;
	generateState(len?: number): string;
	getAllowedOrigin(): string;
	exchangeToken(
		shortToken: string,
	): Promise<{ success: boolean; token?: string; token_type?: string; error?: string }>;
	getToken(): Promise<string | null>;
	logout(): Promise<void>;
	fetchUser(): Promise<any>;
	login(email: string): Promise<any>;
}

function toOrigin(url: string): string {
	try {
		const u = new URL(url);
		return `${u.protocol}//${u.host}`;
	} catch {
		// Fallback: best-effort origin extraction
		const m = url.match(/^((https?:)\/\/[^\/]+)\//i);
		return m?.[1] || '';
	}
}

function randomState(len: number = 16): string {
	const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
	let out = '';
	for (let i = 0; i < len; i++) {
		out += alphabet.charAt(Math.floor(Math.random() * alphabet.length));
	}
	return out;
}

class DefaultAuthService implements AuthService {
	private api = ApiFactory.getApiClient();
	private _clearingExpired = false;

	getLoginUrl(state?: string): string {
		// Pass state as the third param; leave deepLinkBase undefined for now
		const route = Routes.auth.login('mobile', undefined, state);

		// For WebView, we need to ensure we're using the right host
		// based on platform to properly connect to the middleware API
		let baseUrl = this.api.baseUrl;

		// For Android, replace localhost with ******** if present
		if (Platform.OS === 'android' && baseUrl.includes('localhost')) {
			baseUrl = baseUrl.replace('localhost', '********');
			if (__DEV__) console.log('Android detected, using emulator special IP:', baseUrl);
		}
		// For iOS simulator, keep localhost as is - shared cookies should handle this
		if (Platform.OS === 'ios' && __DEV__) {
			console.log('iOS detected, using standard localhost');
		}

		// Build the URL
		const url = buildRouteUrl(baseUrl, route);
		if (__DEV__) console.log('Login URL:', url);

		return url;
	}

	generateState(len?: number): string {
		return randomState(len);
	}

	getAllowedOrigin(): string {
		// Extract origin from API URL
		return toOrigin(this.api.baseUrl);
	}

	async exchangeToken(
		shortToken: string,
	): Promise<{ success: boolean; token?: string; token_type?: string; error?: string }> {
		try {
			// Get the API base URL (now includes platform-specific adjustments)
			const baseUrl = this.api.baseUrl;

			// Direct HTTP call instead of using the ApiClient to avoid retries
			const url = `${baseUrl}/exchange-token`;

			if (__DEV__) console.log('Exchanging token with URL:', url);

			const response = await fetch(url, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ token: shortToken }),
				credentials: 'include', // Include cookies with the request
			});

			if (!response.ok) {
				throw new Error(`HTTP ${response.status} ${response.statusText}`);
			}

			const data = await response.json();
			const token = data?.token;
			const tokenType = data?.token_type;

			if (token) {
				await this.persistAuth(String(token), tokenType || 'Bearer');
				return { success: true, token, token_type: tokenType };
			}

			return { success: false, error: 'No token returned from exchange' };
		} catch (e: any) {
			// Log error but DO NOT retry - just report the error once
			console.error('[AuthScreen] Token exchange failed:', e?.message || 'Unknown error');
			return { success: false, error: e?.message || 'Network error' };
		}
	}

	async getToken(): Promise<string | null> {
		try {
			const auth = await secureStorageRepository.getAuthData();

			// Check token validity by expiration time if available
			if (auth?.token && auth.expiresAt) {
				const now = Date.now();
				if (now >= auth.expiresAt) {
					if (!this._clearingExpired) {
						this._clearingExpired = true;
						if (__DEV__) console.warn('Token expired, clearing authentication data');
						// Clear locally to avoid any potential server call loops
						try {
							await secureStorageRepository.clearAuthData();
						} finally {
							this._clearingExpired = false;
						}
					}
					return null;
				}
			}

			return auth?.token ?? null;
		} catch {
			return null;
		}
	}

	async logout(): Promise<void> {
		try {
			// Attempt server-side logout to invalidate the Sanctum token
			const token = await this.getToken();
			const baseUrl = this.api.baseUrl;
			if (token) {
				const url = `${baseUrl}/logout`;
				if (__DEV__) console.log('Logging out via server endpoint:', url);
				try {
					const response = await fetch(url, {
						method: 'POST',
						headers: {
							Authorization: `Bearer ${token}`,
							Accept: 'application/json',
						},
						credentials: 'include',
					});
					if (!response.ok && __DEV__) {
						console.warn('Server logout returned non-OK status:', response.status);
					}
				} catch (e: any) {
					if (__DEV__) console.warn('Server logout failed (continuing with local clear):', e?.message);
				}
			}
		} finally {
			// Always clear local auth data
			try {
				await secureStorageRepository.clearAuthData();
			} catch {
				// no-op
			}
		}
	}

	async login(email: string): Promise<any> {
		try {
			if (__DEV__) console.log(`Attempting dev login for email: ${email}`);

			// Get the API base URL (now includes platform-specific adjustments)
			const baseUrl = this.api.baseUrl;

			if (__DEV__) console.log('Login route:', Routes.auth.devLogin(email));

			// Use direct fetch to avoid network issues on Android
			const devLoginUrl = `${baseUrl}/dev-login`;
			if (__DEV__) console.log('Dev login URL:', devLoginUrl);

			const response = await fetch(devLoginUrl, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ email }),
				credentials: 'include', // Include cookies with the request
			});

			if (!response.ok) {
				throw new Error(`HTTP ${response.status} ${response.statusText}`);
			}

			const data = await response.json();

			if (__DEV__) console.log('Dev login response:', data);

			if (data?.token) {
				// Store the token for future authenticated requests
				await this.persistAuth(data.token, 'Bearer');

				// Fetch user profile after login
				return await this.fetchUser();
			} else {
				throw new Error('No token returned from login');
			}
		} catch (e: any) {
			if (__DEV__) console.error('Dev login failed:', e?.message || 'Unknown error');
			throw new Error(e?.message || 'Login failed');
		}
	}

	async fetchUser(): Promise<any> {
		try {
			// Get the token to verify it exists before making the request
			const token = await this.getToken();
			if (!token) {
				if (__DEV__)
					console.warn('No authentication token available for user profile fetch');
				throw new Error('Authentication required');
			}
			if (__DEV__) console.log('Authentication token available, length:', token.length);

			// Get the API base URL (now includes platform-specific adjustments)
			const baseUrl = this.api.baseUrl;

			// Direct HTTP call instead of using the ApiClient to avoid retries
			const url = `${baseUrl}/user`;

			if (__DEV__) console.log('Fetching user profile from URL:', url);

			const response = await fetch(url, {
				method: 'GET',
				headers: {
					Authorization: `Bearer ${token}`,
					Accept: 'application/json',
				},
				credentials: 'include', // Include cookies with the request
			});

			// Handle 401 Unauthorized errors by clearing the invalid token
			if (response.status === 401) {
				if (__DEV__) console.warn('💥 Received 401 Unauthorized - clearing invalid token');
				await this.logout(); // Clear the invalid token
				throw new Error('Authentication token invalid or expired. Please log in again.');
			}

			if (!response.ok) {
				throw new Error(`HTTP ${response.status} ${response.statusText}`);
			}

			const userData = await response.json();

			// Log response for debugging
			if (__DEV__) {
				console.log('User profile fetch response:', userData);
			}

			if (!userData) {
				throw new Error('Failed to fetch user profile');
			}

			console.log('User profile fetch completed successfully:', userData);
			return userData;
		} catch (e: any) {
			if (__DEV__)
				console.warn('⚠️ User profile fetch failed:', e?.message || 'Unknown error');

			// Add additional debugging for common error cases
			if (__DEV__) {
				if (e?.message?.includes('HTTP 404')) {
					console.error(
						'User endpoint not found. Check that the route is configured correctly.',
					);
				} else if (e?.message?.includes('HTTP 401')) {
					console.error('Authentication failed. Your token may be invalid or expired.');
				} else if (e?.message?.includes('HTTP 403')) {
					console.error(
						"Authorization failed. You don't have permission to access this resource.",
					);
				} else if (e?.message?.includes('Network Error')) {
					console.error(
						'Network error. Check your internet connection or server availability.',
					);
				}
			}

			// Throw appropriate error type based on the error message
			if (e?.message?.includes('HTTP 401')) {
				throw new Error('Authentication failed. Please log in again.');
			} else if (e?.message?.includes('HTTP 403')) {
				throw new Error("You don't have permission to access this resource.");
			} else if (
				e?.message?.includes('Network Error') ||
				e?.message?.includes('Failed to fetch')
			) {
				throw new Error('Network error. Please check your connection and try again.');
			}

			// For any other errors
			throw new Error('Failed to fetch user profile: ' + (e?.message || 'Unknown error'));
		}
	}

	private async persistAuth(token: string, tokenType: string): Promise<void> {
		const authData: AuthData = {
			token,
			refreshToken: '',
			expiresAt: Date.now() + 60 * 60 * 1000,
			tokenType: tokenType || 'Bearer',
		};
		await secureStorageRepository.saveAuthData(authData);
	}
}

export const createAuthService = async (): Promise<AuthService> => {
	return new DefaultAuthService();
};
