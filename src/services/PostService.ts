import { ApiFactory } from '@/services/apiFactory';
import { Post } from '@/features/posts/model/post';
import { Routes } from '@/api/routes';
import { Route } from 'expo-router/build/Route';

export class PostService {
	private apiClient = ApiFactory.getApiClient();

	async fetchPosts(): Promise<Post[]> {
		if (__DEV__) {
			console.log('🔍 PostService: Fetching posts...');
			console.log('🔍 PostService: API base URL:', this.apiClient.baseUrl);
			console.log('🔍 PostService: Posts route:', Routes.posts.fetch());
		}

		const { data } = await this.apiClient.request(Routes.posts.fetch());

		if (__DEV__) {
			console.log('🔍 PostService: Posts fetch successful, data:', data);
		}

		return data['data'];
	}

	async submitPost(title: string, message: string): Promise<boolean> {
		await this.apiClient.request(Routes.posts.post(title, message));
		return true;
	}
}
